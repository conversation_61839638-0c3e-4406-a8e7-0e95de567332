# Visualização Gráfica de Moedas

Este projeto cria gráficos de linha para visualizar dados de moedas (USD, EUR, GBP, JPY, AUD, NZD, CAD, CHF) ao longo do tempo, com fundo preto e cores específicas para cada moeda.

## Arquivos

- `visualizacao_moedas.ipynb` - Notebook Jupyter com análise completa
- `visualizar_graficos.py` - Script Python para execução direta
- `requirements.txt` - Dependências necessárias
- `D1_1.csv`, `H4_1.csv`, `H4_2.csv`, `H4_3.csv` - Arquivos de dados

## Cores das Moedas

- **USD**: Vermelho (#FF0000)
- **EUR**: Verde <PERSON> (#228B22)
- **GBP**: Orquídea Escura (#9932CC)
- **JPY**: Azul Real (#4169E1)
- **AUD**: <PERSON><PERSON><PERSON> (#AFEEEE)
- **NZD**: <PERSON><PERSON> (#800000)
- **CAD**: <PERSON><PERSON> (#FFA500)
- **CHF**: <PERSON> (#D2B48C)

## Como Usar

### Opção 1: Notebook Jupyter (Recomendado)

1. Instale as dependências:
```bash
pip install -r requirements.txt
```

2. Abra o Jupyter Notebook:
```bash
jupyter notebook visualizacao_moedas.ipynb
```

3. Execute todas as células para ver os gráficos

### Opção 2: Script Python

1. Instale as dependências:
```bash
pip install pandas matplotlib numpy
```

2. Execute o script:
```bash
python visualizar_graficos.py
```

## Funcionalidades

### 1. Gráfico Combinado
- Mostra todas as 4 planilhas em uma única visualização (2x2)
- Fundo preto com grid pontilhado
- Cores específicas para cada moeda

### 2. Gráficos Individuais
- Um gráfico detalhado para cada planilha
- Estatísticas básicas (período, total de registros, médias)
- Formato maior para melhor visualização

### 3. Análise Comparativa (apenas no notebook)
- Compara todas as planilhas por moeda
- Mostra como cada moeda se comporta em diferentes datasets

## Características dos Gráficos

- **Fundo preto** para facilitar a visualização
- **Grid pontilhado** para referência
- **Legendas** com cores correspondentes
- **Eixo X** formatado para mostrar data/hora
- **Linhas suaves** com transparência
- **Títulos** destacados em branco

## Estrutura dos Dados

Cada arquivo CSV deve ter as colunas:
- `timestamp` - Data e hora
- `USD`, `EUR`, `GBP`, `JPY`, `AUD`, `NZD`, `CAD`, `CHF` - Valores das moedas

## Requisitos do Sistema

- Python 3.7+
- pandas
- matplotlib
- numpy
- jupyter (para notebook)

## Exemplo de Uso

```python
# Executar apenas um gráfico específico
import pandas as pd
import matplotlib.pyplot as plt

# Configurar estilo
plt.style.use('dark_background')

# Carregar dados
df = pd.read_csv('D1_1.csv')
df['timestamp'] = pd.to_datetime(df['timestamp'])

# Criar gráfico
plt.figure(figsize=(16, 8))
plt.plot(df['timestamp'], df['USD'], color='#FF0000', label='USD')
plt.plot(df['timestamp'], df['EUR'], color='#228B22', label='EUR')
# ... outras moedas

plt.legend()
plt.grid(True, linestyle='--', alpha=0.3)
plt.show()
```

{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Visualização Gráfica das 4 Planilhas de Moedas\n", "\n", "Este notebook cria gráficos de linha para visualizar os dados das 4 planilhas de moedas ao longo do tempo."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import matplotlib.dates as mdates\n", "from datetime import datetime\n", "import numpy as np\n", "\n", "# Configurar o estilo dos gráficos\n", "plt.style.use('dark_background')\n", "plt.rcParams['figure.facecolor'] = 'black'\n", "plt.rcParams['axes.facecolor'] = 'black'\n", "plt.rcParams['savefig.facecolor'] = 'black'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Definir as cores para cada moeda conforme especificado\n", "cores_moedas = {\n", "    'USD': '#FF0000',      # Red\n", "    'EUR': '#228B22',      # ForestGreen\n", "    'GBP': '#9932CC',      # DarkOrchid\n", "    'JPY': '#4169E1',      # RoyalBlue\n", "    'AUD': '#AFEEEE',      # PaleTurquoise\n", "    'NZD': '#800000',      # <PERSON><PERSON>\n", "    'CAD': '#FFA500',      # Orange\n", "    'CHF': '#D2B48C'       # Tan\n", "}\n", "\n", "# Lista dos arquivos CSV\n", "arquivos = ['D1_1.csv', 'H4_1.csv', 'H4_2.csv', 'H4_3.csv']\n", "titulos = ['D1_1 - <PERSON><PERSON>', 'H4_1 - <PERSON><PERSON> H4 (1)', 'H4_2 - <PERSON><PERSON> H4 (2)', 'H4_3 - <PERSON><PERSON> H4 (3)']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def carregar_e_processar_dados(arquivo):\n", "    \"\"\"Carrega e processa os dados de um arquivo CSV\"\"\"\n", "    df = pd.read_csv(arquivo)\n", "    # Converter timestamp para datetime\n", "    df['timestamp'] = pd.to_datetime(df['timestamp'])\n", "    return df\n", "\n", "def criar_grafico(df, titulo, ax):\n", "    \"\"\"Cria um gráfico de linha para os dados\"\"\"\n", "    # Plotar cada moeda com sua cor específica\n", "    for moeda in ['USD', 'EUR', 'GBP', 'JPY', 'AUD', 'NZD', 'CAD', 'CHF']:\n", "        ax.plot(df['timestamp'], df[moeda], \n", "                color=cores_moedas[moeda], \n", "                label=moeda, \n", "                linewidth=1.5,\n", "                alpha=0.8)\n", "    \n", "    # Configurar o gráfico\n", "    ax.set_title(titulo, color='white', fontsize=14, fontweight='bold')\n", "    ax.set_xlabel('Tempo', color='white')\n", "    ax.set_ylabel('Valor', color='white')\n", "    \n", "    # Configurar grid pontilhado\n", "    ax.grid(True, linestyle='--', alpha=0.3, color='white')\n", "    \n", "    # Configurar legenda\n", "    ax.legend(loc='upper right', frameon=True, fancybox=True, shadow=True,\n", "              facecolor='black', edgecolor='white', fontsize=10)\n", "    \n", "    # Configurar eixos\n", "    ax.tick_params(colors='white')\n", "    \n", "    # Formatar eixo x para mostrar datas de forma mais legível\n", "    ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))\n", "    ax.xaxis.set_major_locator(mdates.HourLocator(interval=6))\n", "    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Criar figura com 4 subplots (2x2)\n", "fig, axes = plt.subplots(2, 2, figsize=(20, 12))\n", "fig.suptitle('Gráficos de Moedas - <PERSON><PERSON><PERSON>', color='white', fontsize=18, fontweight='bold')\n", "\n", "# Achatar o array de axes para facilitar iteração\n", "axes_flat = axes.flatten()\n", "\n", "# Criar um gráfico para cada arquivo\n", "for i, (arquivo, titulo) in enumerate(zip(arquivos, titulos)):\n", "    print(f\"Processando {arquivo}...\")\n", "    df = carregar_e_processar_dados(arquivo)\n", "    criar_grafico(df, titulo, axes_flat[i])\n", "\n", "# Ajustar layout\n", "plt.tight_layout()\n", "plt.subplots_adjust(top=0.93)\n", "\n", "# Mostrar o gráfico\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Criar gráficos individuais maiores para cada planilha\n", "for i, (arquivo, titulo) in enumerate(zip(arquivos, titulos)):\n", "    print(f\"Criando gráfico individual para {arquivo}...\")\n", "    \n", "    # Carregar dados\n", "    df = carregar_e_processar_dados(arquivo)\n", "    \n", "    # Criar figura individual\n", "    fig, ax = plt.subplots(1, 1, figsize=(16, 8))\n", "    \n", "    # <PERSON><PERSON><PERSON> gr<PERSON><PERSON>o\n", "    criar_grafico(df, titulo, ax)\n", "    \n", "    # Mostrar estatísticas básicas\n", "    print(f\"\\nEstatísticas para {arquivo}:\")\n", "    print(f\"Período: {df['timestamp'].min()} até {df['timestamp'].max()}\")\n", "    print(f\"Total de registros: {len(df)}\")\n", "    \n", "    # Mostrar valores médios por moeda\n", "    print(\"\\nValores médios por moeda:\")\n", "    for moeda in ['USD', 'EUR', 'GBP', 'JPY', 'AUD', 'NZD', 'CAD', 'CHF']:\n", "        media = df[moeda].mean()\n", "        print(f\"{moeda}: {media:.2f}\")\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    print(\"\\n\" + \"=\"*50 + \"\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Análise comparativa - most<PERSON> todas as planil<PERSON> em um único gráfico por moeda\n", "print(\"Criando análise comparativa por moeda...\")\n", "\n", "# <PERSON>egar todos os dados\n", "dados = {}\n", "for arquivo, titulo in zip(arquivos, titulos):\n", "    dados[titulo] = carregar_e_processar_dados(arquivo)\n", "\n", "# Criar gráfico comparativo para cada moeda\n", "fig, axes = plt.subplots(2, 4, figsize=(24, 12))\n", "fig.suptitle('Análise Comparativa por Moeda - <PERSON><PERSON> as <PERSON><PERSON><PERSON>', color='white', fontsize=18, fontweight='bold')\n", "\n", "axes_flat = axes.flatten()\n", "moedas = ['USD', 'EUR', 'GBP', 'JPY', 'AUD', 'NZD', 'CAD', 'CHF']\n", "\n", "for i, moeda in enumerate(moedas):\n", "    ax = axes_flat[i]\n", "    \n", "    # Plotar cada planilha para esta moeda\n", "    cores_planilhas = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']\n", "    \n", "    for j, (titulo, df) in enumerate(dados.items()):\n", "        ax.plot(df['timestamp'], df[moeda], \n", "                color=cores_planilhas[j], \n", "                label=titulo.split(' - ')[0], \n", "                linewidth=1.5,\n", "                alpha=0.8)\n", "    \n", "    # Configurar o gráfico\n", "    ax.set_title(f'{moeda}', color=cores_moedas[moeda], fontsize=14, fontweight='bold')\n", "    ax.set_xlabel('Tempo', color='white', fontsize=10)\n", "    ax.set_ylabel('Valor', color='white', fontsize=10)\n", "    \n", "    # Grid pontilhado\n", "    ax.grid(True, linestyle='--', alpha=0.3, color='white')\n", "    \n", "    # Legenda\n", "    ax.legend(loc='upper right', fontsize=8, frameon=True, \n", "              facecolor='black', edgecolor='white')\n", "    \n", "    # Configurar eixos\n", "    ax.tick_params(colors='white', labelsize=8)\n", "    \n", "    # Formatar eixo x\n", "    ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))\n", "    ax.xaxis.set_major_locator(mdates.DayLocator(interval=1))\n", "    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')\n", "\n", "plt.tight_layout()\n", "plt.subplots_adjust(top=0.93)\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}
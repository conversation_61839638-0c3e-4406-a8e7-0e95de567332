#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Visualização Gráfica das 4 Planilhas de Moedas
Cria gráficos de linha com fundo preto e cores específicas para cada moeda
"""

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import numpy as np

# Configurar o estilo dos gráficos para fundo preto
plt.style.use('dark_background')
plt.rcParams['figure.facecolor'] = 'black'
plt.rcParams['axes.facecolor'] = 'black'
plt.rcParams['savefig.facecolor'] = 'black'

# Definir as cores para cada moeda conforme especificado
cores_moedas = {
    'USD': '#FF0000',      # Red
    'EUR': '#228B22',      # ForestGreen
    'GBP': '#9932CC',      # DarkOrchid
    'JPY': '#4169E1',      # RoyalBlue
    'AUD': '#AFEEEE',      # PaleTurquoise
    'NZD': '#800000',      # Maroon
    'CAD': '#FFA500',      # Orange
    'CHF': '#D2B48C'       # Tan
}

# Lista dos arquivos CSV
arquivos = ['D1_1.csv', 'H4_1.csv', 'H4_2.csv', 'H4_3.csv']
titulos = ['D1_1 - Dados Diários', 'H4_1 - Dados H4 (1)', 'H4_2 - Dados H4 (2)', 'H4_3 - Dados H4 (3)']

def carregar_e_processar_dados(arquivo):
    """Carrega e processa os dados de um arquivo CSV"""
    try:
        df = pd.read_csv(arquivo)
        # Converter timestamp para datetime
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        return df
    except FileNotFoundError:
        print(f"Erro: Arquivo {arquivo} não encontrado!")
        return None
    except Exception as e:
        print(f"Erro ao carregar {arquivo}: {e}")
        return None

def criar_grafico(df, titulo, ax):
    """Cria um gráfico de linha para os dados"""
    # Plotar cada moeda com sua cor específica
    for moeda in ['USD', 'EUR', 'GBP', 'JPY', 'AUD', 'NZD', 'CAD', 'CHF']:
        if moeda in df.columns:
            ax.plot(df['timestamp'], df[moeda], 
                    color=cores_moedas[moeda], 
                    label=moeda, 
                    linewidth=1.5,
                    alpha=0.8)
    
    # Configurar o gráfico
    ax.set_title(titulo, color='white', fontsize=14, fontweight='bold')
    ax.set_xlabel('Tempo', color='white')
    ax.set_ylabel('Valor', color='white')
    
    # Configurar grid pontilhado
    ax.grid(True, linestyle='--', alpha=0.3, color='white')
    
    # Configurar legenda
    ax.legend(loc='upper right', frameon=True, fancybox=True, shadow=True,
              facecolor='black', edgecolor='white', fontsize=10)
    
    # Configurar eixos
    ax.tick_params(colors='white')
    
    # Formatar eixo x para mostrar datas de forma mais legível
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d %H:%M'))
    ax.xaxis.set_major_locator(mdates.HourLocator(interval=6))
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')

def main():
    """Função principal"""
    print("=== Visualização Gráfica das Moedas ===\n")
    
    # Verificar se os arquivos existem
    arquivos_existentes = []
    for arquivo in arquivos:
        df = carregar_e_processar_dados(arquivo)
        if df is not None:
            arquivos_existentes.append((arquivo, df))
    
    if not arquivos_existentes:
        print("Nenhum arquivo CSV foi encontrado!")
        return
    
    print(f"Encontrados {len(arquivos_existentes)} arquivos para processar.\n")
    
    # 1. Criar gráfico com todos os 4 subplots
    if len(arquivos_existentes) >= 2:
        print("Criando gráfico combinado...")
        
        # Determinar layout dos subplots
        if len(arquivos_existentes) == 4:
            rows, cols = 2, 2
        elif len(arquivos_existentes) == 3:
            rows, cols = 2, 2
        else:
            rows, cols = 1, 2
        
        fig, axes = plt.subplots(rows, cols, figsize=(20, 12))
        fig.suptitle('Gráficos de Moedas - Análise Temporal', color='white', fontsize=18, fontweight='bold')
        
        # Achatar o array de axes se necessário
        if len(arquivos_existentes) > 1:
            axes_flat = axes.flatten() if hasattr(axes, 'flatten') else [axes]
        else:
            axes_flat = [axes]
        
        # Criar um gráfico para cada arquivo
        for i, (arquivo, df) in enumerate(arquivos_existentes):
            if i < len(axes_flat):
                titulo = titulos[arquivos.index(arquivo)]
                criar_grafico(df, titulo, axes_flat[i])
        
        # Ocultar subplots vazios se necessário
        for i in range(len(arquivos_existentes), len(axes_flat)):
            axes_flat[i].set_visible(False)
        
        plt.tight_layout()
        plt.subplots_adjust(top=0.93)

        # Salvar o gráfico combinado
        plt.savefig('grafico_combinado_moedas.png', dpi=300, bbox_inches='tight',
                   facecolor='black', edgecolor='none')
        print("Gráfico combinado salvo como 'grafico_combinado_moedas.png'")
        plt.close()
    
    # 2. Criar gráficos individuais
    print("\nCriando gráficos individuais...")
    for arquivo, df in arquivos_existentes:
        titulo = titulos[arquivos.index(arquivo)]
        print(f"Processando {arquivo}...")
        
        # Criar figura individual
        fig, ax = plt.subplots(1, 1, figsize=(16, 8))
        
        # Criar gráfico
        criar_grafico(df, titulo, ax)
        
        # Mostrar estatísticas básicas
        print(f"\nEstatísticas para {arquivo}:")
        print(f"Período: {df['timestamp'].min()} até {df['timestamp'].max()}")
        print(f"Total de registros: {len(df)}")
        
        # Mostrar valores médios por moeda
        print("\nValores médios por moeda:")
        for moeda in ['USD', 'EUR', 'GBP', 'JPY', 'AUD', 'NZD', 'CAD', 'CHF']:
            if moeda in df.columns:
                media = df[moeda].mean()
                print(f"{moeda}: {media:.2f}")
        
        plt.tight_layout()

        # Salvar gráfico individual
        nome_arquivo = f"grafico_{arquivo.replace('.csv', '')}.png"
        plt.savefig(nome_arquivo, dpi=300, bbox_inches='tight',
                   facecolor='black', edgecolor='none')
        print(f"Gráfico salvo como '{nome_arquivo}'")
        plt.close()
        print("\n" + "="*50 + "\n")
    
    print("Visualização concluída!")

if __name__ == "__main__":
    main()
